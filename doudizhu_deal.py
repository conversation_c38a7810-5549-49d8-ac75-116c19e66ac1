import turtle
import random
import time

class DouD<PERSON><PERSON>huDealer:
    def __init__(self):
        # 初始化turtle画布
        self.screen = turtle.Screen()
        self.screen.setup(1200, 800)
        self.screen.bgcolor("green")
        self.screen.title("斗地主发牌系统")
        
        # 创建画笔
        self.dealer = turtle.Turtle()
        self.dealer.speed(0)
        self.dealer.penup()
        
        # 创建扑克牌
        self.cards = self.create_deck()
        
        # 玩家手牌
        self.player1_cards = []
        self.player2_cards = []
        self.player3_cards = []
        self.bottom_cards = []
        
        # 位置设置
        self.positions = {
            'player1': (-400, -200),  # 左下
            'player2': (0, 300),      # 上方
            'player3': (400, -200),   # 右下
            'bottom': (0, 0),         # 中央底牌
            'deck': (0, -300)         # 牌堆位置
        }
    
    def create_deck(self):
        """创建一副完整的扑克牌"""
        suits = ['♠', '♥', '♣', '♦']  # 黑桃、红心、梅花、方块
        ranks = ['3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A', '2']
        
        cards = []
        # 添加普通牌
        for suit in suits:
            for rank in ranks:
                cards.append(f"{suit}{rank}")
        
        # 添加大小王
        cards.append("小王")
        cards.append("大王")
        
        return cards
    
    def shuffle_cards(self):
        """洗牌"""
        random.shuffle(self.cards)
        self.show_message("正在洗牌...", (0, 100))
        time.sleep(1)
    
    def show_message(self, message, position):
        """显示消息"""
        self.dealer.goto(position)
        self.dealer.clear()
        self.dealer.write(message, align="center", font=("Arial", 16, "bold"))
    
    def draw_card(self, position, card_text, color="black"):
        """绘制一张牌"""
        x, y = position
        
        # 绘制卡牌背景
        self.dealer.goto(x, y)
        self.dealer.pendown()
        self.dealer.fillcolor("white")
        self.dealer.begin_fill()
        
        # 绘制矩形卡牌
        for _ in range(2):
            self.dealer.forward(30)
            self.dealer.left(90)
            self.dealer.forward(40)
            self.dealer.left(90)
        
        self.dealer.end_fill()
        self.dealer.penup()
        
        # 写入卡牌内容
        self.dealer.goto(x + 15, y + 10)
        self.dealer.color(color)
        self.dealer.write(card_text, align="center", font=("Arial", 8, "bold"))
        self.dealer.color("black")
    
    def draw_card_back(self, position):
        """绘制牌背面"""
        x, y = position
        
        self.dealer.goto(x, y)
        self.dealer.pendown()
        self.dealer.fillcolor("blue")
        self.dealer.begin_fill()
        
        # 绘制矩形卡牌
        for _ in range(2):
            self.dealer.forward(30)
            self.dealer.left(90)
            self.dealer.forward(40)
            self.dealer.left(90)
        
        self.dealer.end_fill()
        self.dealer.penup()
    
    def deal_cards(self):
        """发牌过程"""
        self.show_message("开始发牌！", (0, 200))
        time.sleep(1)
        
        # 清空屏幕
        self.dealer.clear()
        
        # 绘制玩家标签
        self.draw_player_labels()
        
        # 发牌：每轮给每个玩家发一张，共发17轮
        card_index = 0
        for round_num in range(17):
            self.show_message(f"第 {round_num + 1} 轮发牌", (0, 350))
            
            # 给玩家1发牌
            card = self.cards[card_index]
            self.player1_cards.append(card)
            pos_x = self.positions['player1'][0] + (len(self.player1_cards) - 1) * 35
            pos_y = self.positions['player1'][1]
            self.draw_card((pos_x, pos_y), card, self.get_card_color(card))
            card_index += 1
            time.sleep(0.3)
            
            # 给玩家2发牌
            card = self.cards[card_index]
            self.player2_cards.append(card)
            pos_x = self.positions['player2'][0] + (len(self.player2_cards) - 1) * 35
            pos_y = self.positions['player2'][1]
            self.draw_card((pos_x, pos_y), card, self.get_card_color(card))
            card_index += 1
            time.sleep(0.3)
            
            # 给玩家3发牌
            card = self.cards[card_index]
            self.player3_cards.append(card)
            pos_x = self.positions['player3'][0] + (len(self.player3_cards) - 1) * 35
            pos_y = self.positions['player3'][1]
            self.draw_card((pos_x, pos_y), card, self.get_card_color(card))
            card_index += 1
            time.sleep(0.3)
        
        # 剩余3张作为底牌
        self.bottom_cards = self.cards[card_index:card_index + 3]
        self.show_message("发牌完成！底牌：", (0, 50))
        
        # 显示底牌（背面）
        for i, card in enumerate(self.bottom_cards):
            pos_x = self.positions['bottom'][0] + i * 40 - 40
            pos_y = self.positions['bottom'][1]
            self.draw_card_back((pos_x, pos_y))
        
        # 显示统计信息
        self.show_statistics()
    
    def get_card_color(self, card):
        """获取卡牌颜色"""
        if '♥' in card or '♦' in card or '王' in card:
            return "red"
        return "black"
    
    def draw_player_labels(self):
        """绘制玩家标签"""
        self.dealer.goto(self.positions['player1'][0], self.positions['player1'][1] - 60)
        self.dealer.write("玩家1", align="left", font=("Arial", 12, "bold"))
        
        self.dealer.goto(self.positions['player2'][0], self.positions['player2'][1] + 60)
        self.dealer.write("玩家2", align="center", font=("Arial", 12, "bold"))
        
        self.dealer.goto(self.positions['player3'][0], self.positions['player3'][1] - 60)
        self.dealer.write("玩家3", align="right", font=("Arial", 12, "bold"))
        
        self.dealer.goto(self.positions['bottom'][0], self.positions['bottom'][1] - 60)
        self.dealer.write("底牌", align="center", font=("Arial", 12, "bold"))
    
    def show_statistics(self):
        """显示统计信息"""
        stats_text = f"玩家1: {len(self.player1_cards)}张  玩家2: {len(self.player2_cards)}张  玩家3: {len(self.player3_cards)}张  底牌: {len(self.bottom_cards)}张"
        self.dealer.goto(0, -350)
        self.dealer.write(stats_text, align="center", font=("Arial", 10, "normal"))
    
    def reveal_bottom_cards(self):
        """揭示底牌"""
        self.show_message("揭示底牌：", (0, 50))
        
        # 清除原来的底牌背面
        for i in range(3):
            pos_x = self.positions['bottom'][0] + i * 40 - 40
            pos_y = self.positions['bottom'][1]
            # 重新绘制底牌正面
            self.draw_card((pos_x, pos_y), self.bottom_cards[i], self.get_card_color(self.bottom_cards[i]))
        
        # 显示底牌内容
        bottom_text = " ".join(self.bottom_cards)
        self.dealer.goto(0, -30)
        self.dealer.write(f"底牌：{bottom_text}", align="center", font=("Arial", 12, "bold"))
    
    def start_game(self):
        """开始游戏"""
        self.show_message("欢迎来到斗地主发牌系统！", (0, 0))
        time.sleep(2)
        
        # 洗牌
        self.shuffle_cards()
        
        # 发牌
        self.deal_cards()
        
        # 等待用户点击揭示底牌
        self.show_message("点击屏幕揭示底牌", (0, -250))
        self.screen.onclick(lambda x, y: self.reveal_bottom_cards())
        
        # 保持窗口打开
        self.screen.exitonclick()

# 运行游戏
if __name__ == "__main__":
    game = DouDiZhuDealer()
    game.start_game()
